<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form for Fillify In-Page Popup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2962FF;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            font-family: inherit;
            box-sizing: border-box;
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .submit-btn {
            background: #2962FF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        .submit-btn:hover {
            background: #1E4EE3;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2962FF;
        }
        
        .instructions h2 {
            margin-top: 0;
            color: #2962FF;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .keyboard-shortcut {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>🎉 Test the New In-Page Popup!</h2>
        <p>The Fillify popup has been converted to an in-page popup. Here's how to use it:</p>
        <ul>
            <li><strong>Keyboard Shortcut:</strong> Press <span class="keyboard-shortcut">Ctrl+Shift+F</span> (or <span class="keyboard-shortcut">Cmd+Shift+F</span> on Mac)</li>
            <li><strong>Right-Click Menu:</strong> Right-click anywhere on the page and select "Open Fillify"</li>
            <li><strong>Features:</strong> All original popup features are preserved in the new in-page version</li>
        </ul>
        <p><strong>Try it now!</strong> Use either method to open the popup and fill out the form below.</p>
    </div>

    <div class="form-container">
        <h1>Sample Contact Form</h1>
        <form id="test-form">
            <div class="form-group">
                <label for="name">Full Name</label>
                <input type="text" id="name" name="name" placeholder="Enter your full name">
            </div>
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="Enter your email address">
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
            </div>
            
            <div class="form-group">
                <label for="company">Company</label>
                <input type="text" id="company" name="company" placeholder="Enter your company name">
            </div>
            
            <div class="form-group">
                <label for="position">Position</label>
                <input type="text" id="position" name="position" placeholder="Enter your job title">
            </div>
            
            <div class="form-group">
                <label for="subject">Subject</label>
                <select id="subject" name="subject">
                    <option value="">Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="support">Technical Support</option>
                    <option value="sales">Sales Question</option>
                    <option value="partnership">Partnership</option>
                    <option value="feedback">Feedback</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" placeholder="Enter your message here..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="priority">Priority</label>
                <select id="priority" name="priority">
                    <option value="">Select priority</option>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="newsletter">
                    <input type="checkbox" id="newsletter" name="newsletter" style="width: auto; margin-right: 8px;">
                    Subscribe to our newsletter
                </label>
            </div>
            
            <button type="submit" class="submit-btn">Submit Form</button>
        </form>
    </div>

    <script>
        // Prevent actual form submission for testing
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form submission prevented for testing purposes. The form fields should be filled by Fillify!');
        });
        
        // Add some console logging to help with debugging
        console.log('Test form loaded. Try using Ctrl+Shift+F or the floating button to open Fillify popup!');
    </script>
</body>
</html>
