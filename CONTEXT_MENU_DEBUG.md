# 右键菜单调试指南

## 🔍 问题诊断

如果右键菜单中没有显示"Open Fillify"选项，请按以下步骤进行调试：

## 📋 检查清单

### 1. 扩展加载状态
- [ ] 确认扩展已正确加载到浏览器
- [ ] 检查扩展图标是否显示在工具栏中
- [ ] 确认没有扩展加载错误

### 2. 权限检查
- [ ] 打开 `chrome://extensions/`
- [ ] 找到Fillify扩展
- [ ] 点击"详细信息"
- [ ] 确认权限列表中包含"contextMenus"

### 3. 控制台日志检查
- [ ] 打开浏览器开发者工具 (F12)
- [ ] 切换到"Console"标签
- [ ] 刷新页面或重新加载扩展
- [ ] 查找以下日志信息：
  - `[Fillify] Context menu created successfully`
  - `[Fillify] Context menu listener setup complete`

## 🛠️ 调试步骤

### 步骤1: 检查扩展Service Worker
1. 打开 `chrome://extensions/`
2. 开启"开发者模式"
3. 找到Fillify扩展，点击"Service Worker"链接
4. 在打开的控制台中查看是否有错误信息
5. 查找右键菜单相关的日志

### 步骤2: 手动重新加载扩展
1. 在 `chrome://extensions/` 页面
2. 找到Fillify扩展
3. 点击刷新按钮 🔄
4. 等待扩展重新加载完成
5. 尝试右键点击页面

### 步骤3: 检查页面URL匹配
右键菜单配置为支持所有URL (`<all_urls>`)，但某些特殊页面可能不支持：
- `chrome://` 页面
- `chrome-extension://` 页面  
- `about:` 页面
- 某些受保护的页面

**解决方案**: 在普通网页上测试，如 `https://www.google.com`

### 步骤4: 检查浏览器版本
确保使用的是支持Manifest V3的Chrome版本：
- Chrome 88+ (推荐Chrome 100+)
- 其他基于Chromium的浏览器

## 🔧 手动测试方法

### 方法1: 使用开发者工具测试
1. 打开任意网页
2. 按F12打开开发者工具
3. 在Console中执行以下代码：
```javascript
chrome.runtime.sendMessage('扩展ID', {action: 'openInPagePopup'})
```

### 方法2: 使用键盘快捷键
如果右键菜单不工作，尝试键盘快捷键：
- Windows/Linux: `Ctrl + Shift + F`
- Mac: `Cmd + Shift + F`

## 🐛 常见问题及解决方案

### 问题1: 权限不足
**症状**: 控制台显示权限错误
**解决方案**: 
1. 重新安装扩展
2. 确认manifest.json中包含contextMenus权限

### 问题2: Service Worker未启动
**症状**: 没有任何日志输出
**解决方案**:
1. 手动点击Service Worker链接启动
2. 重新加载扩展

### 问题3: 页面CSP限制
**症状**: 在某些网站上不显示菜单
**解决方案**:
1. 在其他网站测试
2. 使用键盘快捷键作为替代

### 问题4: 扩展冲突
**症状**: 菜单项被其他扩展覆盖
**解决方案**:
1. 暂时禁用其他扩展测试
2. 检查是否有同名菜单项

## 📊 调试信息收集

如果问题仍然存在，请收集以下信息：

### 浏览器信息
- 浏览器类型和版本
- 操作系统版本
- 是否使用了特殊的浏览器配置

### 扩展信息
- 扩展版本
- 加载方式（开发模式/商店安装）
- 其他已安装的扩展列表

### 错误信息
- Service Worker控制台的完整错误日志
- 页面控制台的相关错误信息
- 扩展详情页面的错误提示

## 🔄 重置方案

如果所有方法都无效，尝试完全重置：

### 完全重置步骤
1. 完全卸载扩展
2. 重启浏览器
3. 清除浏览器缓存
4. 重新安装扩展
5. 测试右键菜单功能

## 📞 获取帮助

如果问题仍然存在，请提供：
1. 浏览器和操作系统信息
2. Service Worker控制台的完整日志
3. 具体的测试页面URL
4. 重现问题的详细步骤

## 🎯 快速验证脚本

在Service Worker控制台中运行以下代码来验证菜单创建：

```javascript
// 检查contextMenus API
console.log('contextMenus API available:', typeof chrome.contextMenus !== 'undefined');

// 手动创建菜单项
if (typeof chrome.contextMenus !== 'undefined') {
  chrome.contextMenus.removeAll(() => {
    chrome.contextMenus.create({
      id: 'test-fillify-menu',
      title: 'Test Fillify Menu',
      contexts: ['page']
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Menu creation error:', chrome.runtime.lastError);
      } else {
        console.log('Test menu created successfully');
      }
    });
  });
}
```

---

**注意**: 右键菜单功能在开发环境中可能不稳定，建议使用生产构建进行测试。
